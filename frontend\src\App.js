import React, { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { CssBaseline, Box } from '@mui/material';
import { Toaster } from 'react-hot-toast';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import { LanguageProvider } from './contexts/LanguageContext';
import { initializeApp } from './firebase/initializeApp';
// تحسينات الأداء للأجهزة المحمولة والأجهزة اللوحية
import './styles/mobile-optimizations.css';
import './styles/tablet-optimizations.css';
import './styles/interaction-fixes.css';
import './styles/touch-effects.css';

// Components
import Login from './components/Login';
import AdminDashboard from './components/AdminDashboard';
import StudentDashboard from './components/StudentDashboard';
import CoursePlayer from './components/CoursePlayer';
import ForgotPassword from './components/ForgotPassword';
import interactionTracker from './services/interactionTracker';
import firebaseInitializer from './firebase/initializeCollections';

// تم نقل إعدادات الثيم إلى LanguageContext

// Protected Route Component
const ProtectedRoute = ({ children, requiredRole }) => {
  const { user, loading } = useAuth();

  // انتظار انتهاء التحميل قبل التوجيه
  if (loading) {
    return (
      <Box sx={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        color: 'white'
      }}>
        <Box sx={{
          width: 40,
          height: 40,
          border: '3px solid rgba(255,255,255,0.3)',
          borderTop: '3px solid white',
          borderRadius: '50%',
          animation: 'spin 1s linear infinite'
        }} />
      </Box>
    );
  }

  if (!user) {
    return <Navigate to="/login" replace />;
  }

  if (requiredRole && user.role !== requiredRole) {
    return <Navigate to="/" replace />;
  }

  return children;
};

// Main App Component
const AppContent = () => {
  const { user, loading } = useAuth();

  // انتظار انتهاء التحميل قبل عرض المحتوى
  if (loading) {
    return (
      <Box sx={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        color: 'white',
        fontFamily: 'Cairo, Arial, sans-serif'
      }}>
        {/* شعار التطبيق */}
        <Box sx={{
          mb: 4,
          textAlign: 'center',
          animation: 'fadeIn 1s ease-in-out'
        }}>
          <Box sx={{
            fontSize: '3rem',
            mb: 2,
            fontWeight: 'bold',
            textShadow: '2px 2px 4px rgba(0,0,0,0.3)'
          }}>
            📚 SKILLS WORLD ACADEMY
          </Box>
          <Box sx={{
            fontSize: '1.2rem',
            opacity: 0.9,
            fontWeight: 300
          }}>
            منصة التعلم والتطوير المهني العالمية
          </Box>
        </Box>

        {/* مؤشر التحميل */}
        <Box sx={{
          width: 60,
          height: 60,
          border: '4px solid rgba(255,255,255,0.3)',
          borderTop: '4px solid white',
          borderRadius: '50%',
          animation: 'spin 1s linear infinite',
          mb: 2
        }} />

        <Box sx={{
          fontSize: '1rem',
          opacity: 0.8,
          animation: 'pulse 2s ease-in-out infinite'
        }}>
          جاري التحميل...
        </Box>

        <style>
          {`
            @keyframes spin {
              0% { transform: rotate(0deg); }
              100% { transform: rotate(360deg); }
            }
            @keyframes fadeIn {
              0% { opacity: 0; transform: translateY(-20px); }
              100% { opacity: 1; transform: translateY(0); }
            }
            @keyframes pulse {
              0%, 100% { opacity: 0.8; }
              50% { opacity: 1; }
            }
          `}
        </style>
      </Box>
    );
  }

  return (
    <Box sx={{ minHeight: '100vh', bgcolor: 'background.default' }}>
      <Routes>
        <Route
          path="/login"
          element={user ? <Navigate to="/" replace /> : <Login />}
        />

        <Route
          path="/forgot-password"
          element={user ? <Navigate to="/" replace /> : <ForgotPassword />}
        />

        <Route
          path="/"
          element={
            user ? (
              user.role === 'admin' ? (
                <Navigate to="/admin" replace />
              ) : (
                <Navigate to="/student" replace />
              )
            ) : (
              <Navigate to="/login" replace />
            )
          }
        />
        
        <Route 
          path="/admin/*" 
          element={
            <ProtectedRoute requiredRole="admin">
              <AdminDashboard />
            </ProtectedRoute>
          } 
        />
        
        <Route 
          path="/student/*" 
          element={
            <ProtectedRoute requiredRole="student">
              <StudentDashboard />
            </ProtectedRoute>
          } 
        />
        
        <Route 
          path="/course/:courseId" 
          element={
            <ProtectedRoute requiredRole="student">
              <CoursePlayer />
            </ProtectedRoute>
          } 
        />
        
        <Route path="*" element={<Navigate to="/" replace />} />
      </Routes>
    </Box>
  );
};

function App() {
  useEffect(() => {
    const initApp = async () => {
      try {
        console.log('🚀 بدء تهيئة التطبيق...');

        // إضافة timeout للتهيئة لمنع التعليق
        const initPromise = initializeApp();
        const timeoutPromise = new Promise((_, reject) =>
          setTimeout(() => reject(new Error('انتهت مهلة التهيئة')), 10000)
        );

        await Promise.race([initPromise, timeoutPromise]);
        console.log('✅ تم تهيئة التطبيق بنجاح');

        // تهيئة جداول Firebase - معطل للإنتاج لمنع إنشاء البيانات التجريبية
        // await firebaseInitializer.initializeAllCollections();
        console.log('🔧 تم تخطي تهيئة جداول Firebase للإنتاج');

        // تفعيل نظام تتبع التفاعلات
        console.log('🔍 تفعيل نظام تتبع التفاعلات الشامل');
        interactionTracker.trackInteraction('app_initialized', {
          timestamp: Date.now(),
          userAgent: navigator.userAgent,
          language: navigator.language,
          platform: navigator.platform
        });
      } catch (error) {
        console.error('خطأ في تهيئة التطبيق:', error);
        interactionTracker.trackError(error, { context: 'app_initialization' });
        // السماح بالمتابعة حتى لو فشلت التهيئة
        console.log('⚠️ المتابعة رغم فشل التهيئة...');
      }
    };

    initApp();
  }, []);

  return (
    <LanguageProvider>
      <CssBaseline />
      <AuthProvider>
        <Router future={{ v7_startTransition: true, v7_relativeSplatPath: true }}>
          <AppContent />
          <Toaster
            position="top-center"
            toastOptions={{
              duration: 4000,
              style: {
                background: '#333',
                color: '#fff',
                fontFamily: 'Cairo, Arial, sans-serif',
              },
              success: {
                iconTheme: {
                  primary: '#4CAF50',
                  secondary: '#fff',
                },
              },
              error: {
                iconTheme: {
                  primary: '#f44336',
                  secondary: '#fff',
                },
              },
            }}
          />
        </Router>
      </AuthProvider>
    </LanguageProvider>
  );
}

export default App;
